/// <reference types="vitest" />
import { defineConfig } from 'vitest/config'

export default defineConfig({
  test: {
    // Run tests from the workspace root
    root: '.',
    
    // Include only vitest/jest-style tests, exclude Playwright tests
    include: [
      'apps/*/src/**/*.test.{js,ts,jsx,tsx}',
      'apps/*/tests/**/*.test.{js,ts,jsx,tsx}',
      'libs/**/*.test.{js,ts,jsx,tsx}'
    ],
    
    // Exclude Playwright tests and e2e tests
    exclude: [
      '**/node_modules/**',
      '**/dist/**',
      '**/*.spec.ts', // Playwright convention
      '**/*.spec.tsx', // Playwright convention
      '**/tests/e2e/**',
      '**/e2e/**',
      'tests/visual-regression/**',
      'apps/giki-ai-e2e/**',
      'apps/**/tests/e2e/**',
      'apps/**/src/test/e2e/**'
    ],
    
    // Global test setup
    globals: true,
    environment: 'jsdom',
    setupFiles: ['./apps/giki-ai-app/src/test/setup.ts'],
    
    // Test timeout
    testTimeout: 10000,
    
    // Environment options for jsdom
    environmentOptions: {
      jsdom: {
        resources: 'usable',
      },
    },
    
    // Test reporter
    reporter: ['verbose', 'json'],
    outputFile: './test-results.json'
  }
})